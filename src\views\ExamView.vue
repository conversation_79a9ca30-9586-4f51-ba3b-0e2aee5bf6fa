<template>
  <div>
    <div class="container-fluid">
      <div class="row">
        <!-- 答題卡側邊欄 -->
        <div class="col-lg-3">
          <AnswerCard :questions="questions" :user-answers="userAnswers" :current-index="currentQuestionIndex"
            mode="exam" @jump-to-question="jumpToQuestion" />
        </div>

        <!-- 主要內容區域 -->
        <div class="col-lg-9">
          <div class="exam-card h-100">
            <!-- 返回按鈕 -->
            <BackBtn text="選擇章節" @click="goBack" />

            <div class="text-center mb-3">
              <h5 class="fw-semibold text-muted">
                第 {{ currentQuestionIndex + 1 }} 題 / 共 {{ questions.length }} 題
              </h5>
            </div>

            <div class="progress mb-4" style="height: 8px;">
              <div class="progress-bar bg-gradient" :style="{ width: progressPercentage + '%' }"></div>
            </div>

            <div class="card border-0 bg-light mb-4" v-if="currentQuestion">
              <div class="card-body p-4">
                <h4 class="card-text mb-4">
                  {{ currentQuestionIndex + 1 }}. {{ currentQuestion.q }}
                  <span v-if="currentQuestion.type === 'multiple'" class="badge bg-info ms-2">複選題</span>
                  <span v-else class="badge bg-secondary ms-2">單選題</span>
                </h4>
                <div class="d-grid gap-3">
                  <div v-for="(option, index) in currentQuestion.options" :key="index" class="option-item"
                    :class="{ selected: isOptionSelected(index) }" @click="selectOption(index)">
                    <div class="option-letter">{{ String.fromCharCode(65 + index) }}</div>
                    <div class="flex-grow-1">{{ option }}</div>
                  </div>
                </div>
                <div v-if="currentQuestion.type === 'multiple'" class="mt-3">
                  <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    複選題：可選擇多個答案
                  </small>
                </div>
              </div>
            </div>

            <div class="d-flex justify-content-between mb-4">
              <button class="btn btn-secondary d-flex align-items-center gap-2" @click="prevQuestion"
                :disabled="currentQuestionIndex === 0" v-show="currentQuestionIndex > 0">
                <i class="fas fa-chevron-left"></i>
                上一題
              </button>

              <button class="btn btn-gradient btn-lg d-flex align-items-center gap-2 ms-auto" @click="nextQuestion"
                :disabled="!isAnswered">
                <i class="fas fa-chevron-right" v-if="!isLastQuestion"></i>
                <i class="fas fa-check" v-else></i>
                {{ isLastQuestion ? '提交答案' : '下一題' }}
              </button>
            </div>

            <div class="row g-2 text-center">
              <div class="col-md-4">
                <div class="alert alert-info mb-0 py-2">
                  <i class="fas fa-book me-1"></i>
                  {{ currentChapter?.title }}
                </div>
              </div>
              <div class="col-md-4">
                <div class="alert alert-info mb-0 py-2">
                  <i class="fas fa-language me-1"></i>
                  {{ currentLanguageText }}
                </div>
              </div>
              <div class="col-md-4">
                <div class="alert alert-success mb-0 py-2">
                  <i class="fas fa-check-circle me-1"></i>
                  已答：{{ answeredCount }}/{{ questions.length }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useExamStore } from '../stores/examStore'
import AnswerCard from '../components/AnswerCard.vue'
import BackBtn from '../components/BackBtn.vue'

const router = useRouter()
const examStore = useExamStore()

// 檢查考試狀態
if (!examStore.user.isLoggedIn) {
  router.push('/')
} else if (!examStore.currentLanguage) {
  router.push('/language')
} else if (!examStore.currentChapter) {
  router.push('/chapters')
}

const currentQuestion = computed(() => examStore.getCurrentQuestion)
const currentQuestionIndex = computed(() => examStore.currentQuestionIndex)
const questions = computed(() => examStore.questions)
const userAnswers = computed(() => examStore.userAnswers)
const isLastQuestion = computed(() => examStore.isLastQuestion)
const currentChapter = computed(() => examStore.currentChapter)

const currentLanguageText = computed(() => {
  return examStore.currentLanguage === 'chinese' ? '中文' : 'English'
})

const progressPercentage = computed(() => {
  return ((currentQuestionIndex.value + 1) / questions.value.length) * 100
})

const answeredCount = computed(() => {
  return userAnswers.value.filter(answer => answer !== -1 && answer !== null).length
})

const isAnswered = computed(() => {
  const currentAnswer = userAnswers.value[currentQuestionIndex.value]
  if (Array.isArray(currentAnswer)) {
    return currentAnswer.length > 0
  }
  return currentAnswer !== -1 && currentAnswer !== null
})

const isOptionSelected = (index) => {
  const currentAnswer = userAnswers.value[currentQuestionIndex.value]
  if (Array.isArray(currentAnswer)) {
    return currentAnswer.includes(index)
  }
  return currentAnswer === index
}

const selectOption = (index) => {
  const currentAnswer = userAnswers.value[currentQuestionIndex.value]

  if (currentQuestion.value.type === 'multiple') {
    // 複選題邏輯
    let newAnswer = Array.isArray(currentAnswer) ? [...currentAnswer] : []

    if (newAnswer.includes(index)) {
      // 取消選擇
      newAnswer = newAnswer.filter(ans => ans !== index)
    } else {
      // 添加選擇
      newAnswer.push(index)
    }

    examStore.selectAnswer(newAnswer)
  } else {
    // 單選題邏輯
    examStore.selectAnswer(index)

    // 單選題自動跳到下一題
    setTimeout(() => {
      if (!isLastQuestion.value) {
        examStore.nextQuestion()
      }
    }, 500)
  }
}

const nextQuestion = async () => {
  if (isLastQuestion.value) {
    // 提交答案並獲取新記錄的 ID
    const newRecordId = await examStore.calculateResults()
    if (newRecordId) {
      // 跳轉到考試詳情頁面
      router.push(`/history/${newRecordId}`)
    } else {
      // 如果保存失敗，仍然跳轉到結果頁面
      router.push('/result')
    }
  } else {
    examStore.nextQuestion()
  }
}

const prevQuestion = () => {
  examStore.prevQuestion()
}

const jumpToQuestion = (index) => {
  examStore.currentQuestionIndex = index
}

const goBack = () => {
  router.push('/chapters')
}
</script>

<style scoped>
.bg-gradient {
  background: linear-gradient(45deg, #667eea, #764ba2) !important;
}

@media (max-width: 768px) {
  .exam-card {
    padding: 30px 20px;
  }

  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 15px;
  }

  .ms-auto {
    margin-left: 0 !important;
  }
}
</style>
