<template>
  <div>
    <div class="container-fluid">
      <div class="row">
        <!-- 答題卡側邊欄 - 桌面版 -->
        <div class="col-lg-3 d-none d-lg-block">
          <AnswerCard :questions="questions" :user-answers="userAnswers" :current-index="currentQuestionIndex"
            :marked-questions="markedQuestions" :chapter-title="examStore.currentChapter?.title || ''"
            :current-language="examStore.currentLanguage" :is-option-disabled="isOptionDisabled" mode="exam"
            @jump-to-question="jumpToQuestion" @next-question="nextQuestion" />
        </div>

        <!-- 主要內容區域 -->
        <div class="col-lg-9">
          <div class="exam-card h-100">
            <!-- 頂部導航區域 -->
            <div class="top-nav-area mb-3">
              <!-- 桌面版：返回按鈕 + 標記按鈕 -->
              <div class="d-flex justify-content-between align-items-center">
                <BackBtn text="選擇章節" @click="goBack" />
                <div class="d-inline d-sm-none">{{ getChapterDisplayTitle }}</div>
                <button class="btn btn-outline-warning d-flex align-items-center gap-2" @click="toggleCurrentMark"
                  data-bs-toggle="button">
                  <i class="fas fa-bookmark"></i>
                  <span class="d-none d-sm-inline">{{ isCurrentMarked ? '取消' : '標記' }}</span>
                </button>
              </div>

              <!-- 手機版：章節標題 + 答題卡 -->
              <div class="d-lg-none mt-2">
                <!-- 手機版答題卡 - 預設顯示 -->
                <div class="mobile-answer-card mb-3">
                  <div class="answer-scroll-container">
                    <div class="answer-grid-mobile-horizontal">
                      <button v-for="(_, index) in questions" :key="index" class="answer-btn-mobile"
                        :class="getMobileButtonClass(index)" @click="jumpToQuestion(index)">
                        {{ index + 1 }}
                        <i v-if="getButtonStatus(index) === 'answered'" class="fas fa-check answer-check"></i>
                      </button>
                    </div>
                    <!-- 答題卡按鈕 -->
                    <button class="answer-card-btn" @click="openAnswerCardModal">
                      <i class="fas fa-th-large"></i>
                      <span>答題卡</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="text-center mb-3">
              <h5 class="fw-semibold text-muted">
                第 {{ currentQuestionIndex + 1 }} 題 / 共 {{ questions.length }} 題
              </h5>
            </div>

            <div class="progress mb-4" style="height: 8px;">
              <div class="progress-bar bg-gradient" :style="{ width: progressPercentage + '%' }"></div>
            </div>

            <div class="card border-0 bg-light" v-if="currentQuestion">
              <div class="card-body p-4">
                <h5 class="mb-4 card-text">
                  {{ currentQuestionIndex + 1 }}. {{ currentQuestion.q }}
                  <span v-if="currentQuestion.type === 'multiple'" class="badge bg-info ms-2">複選題</span>
                  <span v-else class="badge bg-secondary ms-2">單選題</span>
                </h5>
                <div class="d-grid gap-sm-3">
                  <div v-for="(option, index) in currentQuestion.options" :key="index" class="option-item"
                    :class="{ selected: isOptionSelected(index), disabled: isOptionDisabled }"
                    @click="!isOptionDisabled && selectOption(index)">
                    <div class="option-letter">{{ String.fromCharCode(65 + index) }}</div>
                    <div class="flex-grow-1">{{ option }}</div>
                  </div>
                </div>
                <div v-if="currentQuestion.type === 'multiple'" class="mt-3">
                  <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    複選題：可選擇多個答案
                  </small>
                </div>
              </div>
            </div>

            <div class="text-muted fs-6 my-2">單選題答完會自動進入下一提目，複選題請點擊下一題按鈕 (或按空白鍵) </div>

            <div class="d-flex justify-content-between mb-4">
              <button class="btn btn-secondary d-flex align-items-center gap-2" @click="prevQuestion"
                :disabled="currentQuestionIndex === 0" v-show="currentQuestionIndex > 0">
                <i class="fas fa-chevron-left"></i>
                上一題
              </button>

              <div class="d-flex">
                <div class="alert alert-success mb-0 py-2 col">
                  <i class="fas fa-check-circle me-1"></i>
                  已答：{{ answeredCount }}/{{ questions.length }}
                </div>
              </div>

              <button class="btn btn-gradient d-flex align-items-center gap-2" @click="nextQuestion"
                :disabled="!isAnswered">
                <i class="fas fa-chevron-right" v-if="!isLastQuestion"></i>
                <i class="fas fa-check" v-else></i>
                {{ isLastQuestion ? '提交答案' : '下一題' }}
              </button>
            </div>

          </div>
        </div>
      </div>
    </div>

    <!-- 手機版答題卡 Modal -->
    <div class="modal fade" id="answerCardModal" tabindex="-1" aria-labelledby="answerCardModalLabel"
      aria-hidden="true">
      <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="answerCardModalLabel">
              <i class="fas fa-th-large me-2"></i>
              答題卡
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body p-0">
            <!-- 完整的桌面版答題卡 -->
            <AnswerCard :questions="questions" :user-answers="userAnswers" :current-index="currentQuestionIndex"
              :marked-questions="markedQuestions" :chapter-title="examStore.currentChapter?.title || ''"
              :current-language="examStore.currentLanguage" :is-option-disabled="isOptionDisabled" mode="exam"
              @jump-to-question="jumpToQuestionFromModal" @next-question="nextQuestion" />
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useExamStore } from '../stores/examStore'
import AnswerCard from '../components/AnswerCard.vue'
import BackBtn from '../components/BackBtn.vue'

const router = useRouter()
const examStore = useExamStore()

// 響應式變量
const markedQuestions = ref([])
const isOptionDisabled = ref(false)

// 檢查考試狀態
if (!examStore.user.isLoggedIn) {
  router.push('/')
} else if (!examStore.currentLanguage) {
  router.push('/language')
} else if (!examStore.currentChapter) {
  router.push('/chapters')
}

const currentQuestion = computed(() => examStore.getCurrentQuestion)
const currentQuestionIndex = computed(() => examStore.currentQuestionIndex)
const questions = computed(() => examStore.questions)
const userAnswers = computed(() => examStore.userAnswers)
const isLastQuestion = computed(() => examStore.isLastQuestion)

const progressPercentage = computed(() => {
  return ((currentQuestionIndex.value + 1) / questions.value.length) * 100
})

const answeredCount = computed(() => {
  return userAnswers.value.filter(answer => answer !== -1 && answer !== null).length
})

const isAnswered = computed(() => {
  const currentAnswer = userAnswers.value[currentQuestionIndex.value]
  if (Array.isArray(currentAnswer)) {
    return currentAnswer.length > 0
  }
  return currentAnswer !== -1 && currentAnswer !== null
})

const isCurrentMarked = computed(() => {
  return markedQuestions.value.includes(currentQuestionIndex.value)
})

const getChapterDisplayTitle = computed(() => {
  const chapterTitle = examStore.currentChapter?.title || ''
  if (!chapterTitle) return ''

  if (examStore.currentLanguage === '中文') {
    return `中文${chapterTitle}`
  } else if (examStore.currentLanguage === '英文') {
    return `英文${chapterTitle}`
  } else {
    return chapterTitle
  }
})

const isOptionSelected = (index) => {
  const currentAnswer = userAnswers.value[currentQuestionIndex.value]
  if (Array.isArray(currentAnswer)) {
    return currentAnswer.includes(index)
  }
  return currentAnswer === index
}

const selectOption = (index) => {
  if (isOptionDisabled.value) return

  const currentAnswer = userAnswers.value[currentQuestionIndex.value]

  if (currentQuestion.value.type === 'multiple') {
    // 複選題邏輯
    let newAnswer = Array.isArray(currentAnswer) ? [...currentAnswer] : []

    if (newAnswer.includes(index)) {
      // 取消選擇
      newAnswer = newAnswer.filter(ans => ans !== index)
    } else {
      // 添加選擇
      newAnswer.push(index)
    }

    examStore.selectAnswer(newAnswer)
  } else {
    // 單選題邏輯
    examStore.selectAnswer(index)

    // 禁用選項點擊
    isOptionDisabled.value = true

    // 單選題更快自動跳到下一題 (200ms)
    setTimeout(() => {
      isOptionDisabled.value = false
      if (!isLastQuestion.value) {
        examStore.nextQuestion()
      }
    }, 200)
  }
}

const nextQuestion = async () => {
  if (isLastQuestion.value) {
    console.log('=== 開始提交考試答案 ===')
    console.log('用戶資訊:', examStore.user)
    console.log('當前語言:', examStore.currentLanguage)
    console.log('當前章節:', examStore.currentChapter)
    console.log('用戶答案:', examStore.userAnswers)
    console.log('題目數量:', examStore.questions.length)

    // 提交答案並獲取新記錄的 ID
    const newRecordId = await examStore.calculateResults()
    console.log('計算結果完成，新記錄 ID:', newRecordId)

    if (newRecordId) {
      console.log('跳轉到考試詳情頁面:', `/history/${newRecordId}`)
      // 等待一下確保 examHistory 已更新
      await new Promise(resolve => setTimeout(resolve, 100))
      // 跳轉到考試詳情頁面
      router.push(`/history/${newRecordId}`)
    } else {
      // 如果保存失敗，創建一個臨時的本地記錄並跳轉
      console.warn('考試記錄保存失敗，創建臨時記錄')
      const tempId = 'temp_' + Date.now()
      const tempRecord = {
        id: tempId,
        date: new Date().toLocaleString('zh-TW'),
        language: examStore.currentLanguage,
        languageText: examStore.currentLanguage === '中文' ? '中文' : 'English',
        chapter: examStore.currentChapter,
        score: examStore.score,
        correctCount: examStore.correctCount,
        totalQuestions: examStore.questions.length,
        incorrectQuestions: [...examStore.incorrectQuestions],
        questions: [...examStore.questions],
        userAnswers: [...examStore.userAnswers],
      }

      // 添加到本地歷史記錄
      examStore.examHistory.unshift(tempRecord)
      console.log('臨時記錄已創建，跳轉到詳情頁面:', `/history/${tempId}`)
      router.push(`/history/${tempId}`)
    }
  } else {
    examStore.nextQuestion()
  }
}

const prevQuestion = () => {
  examStore.prevQuestion()
}

const jumpToQuestion = (index) => {
  examStore.currentQuestionIndex = index
  // 關閉手機版答題卡（如果需要）
  // showMobileAnswerCard.value = false
}

const toggleCurrentMark = () => {
  const index = markedQuestions.value.indexOf(currentQuestionIndex.value)
  if (index > -1) {
    // 取消標記
    markedQuestions.value.splice(index, 1)
  } else {
    // 添加標記
    markedQuestions.value.push(currentQuestionIndex.value)
  }
}

const openAnswerCardModal = () => {
  // 使用 Bootstrap 5 的 Modal API
  const modalElement = document.getElementById('answerCardModal')
  const modal = new window.bootstrap.Modal(modalElement)
  modal.show()
}

const jumpToQuestionFromModal = (index) => {
  examStore.currentQuestionIndex = index
  // 關閉 Modal
  const modalElement = document.getElementById('answerCardModal')
  const modal = window.bootstrap.Modal.getInstance(modalElement)
  if (modal) {
    modal.hide()
  }
}

const getButtonStatus = (index) => {
  const userAnswer = userAnswers.value[index]
  if (userAnswer !== undefined && userAnswer !== -1 && userAnswer !== null) {
    if (Array.isArray(userAnswer) && userAnswer.length > 0) {
      return 'answered'
    } else if (!Array.isArray(userAnswer)) {
      return 'answered'
    }
  }
  return 'unanswered'
}

const getMobileButtonClass = (index) => {
  const classes = []

  if (index === currentQuestionIndex.value) {
    classes.push('current')
  } else if (markedQuestions.value.includes(index)) {
    classes.push('marked')
  } else {
    const status = getButtonStatus(index)
    classes.push(status)
  }

  return classes
}

const goBack = () => {
  router.push('/chapters')
}
</script>

<style scoped>
.bg-gradient {
  background: linear-gradient(45deg, #667eea, #764ba2) !important;
}

.option-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* 手機版答題卡樣式 */
.mobile-answer-card {
  background: #495057;
  border-radius: 12px;
  padding: 6px;
  margin: 0 -15px;
}

.answer-scroll-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.answer-grid-mobile-horizontal {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  flex: 1;
  padding: 5px 0;
  /* 隱藏滾動條但保持滑動功能 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}

.answer-grid-mobile-horizontal::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari, Opera */
}

.answer-btn-mobile {
  width: 40px;
  height: 40px;
  /* 防止收縮 */
  border: none;
  border-radius: 50%;
  background: #6c757d;
  color: white;
  font-weight: bold;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex-shrink: 0;
  /* 防止在 flex 容器中收縮 */
}

.answer-btn-mobile:hover {
  transform: scale(1.1);
}

.answer-btn-mobile.answered {
  background: #56cc9d;
}

.answer-btn-mobile.unanswered {
  background: #6c757d;
}

.answer-btn-mobile.marked {
  background: #ffc107;
  color: #212529;
}

.answer-btn-mobile.current {
  background: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

.answer-check {
  position: absolute;
  bottom: -2px;
  right: -2px;
  background: white;
  color: #56cc9d;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
}

/* 答題卡按鈕樣式 */
.answer-card-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: 2px solid #6c757d;
  border-radius: 8px;
  color: #6c757d;
  padding: 8px;
  height: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.answer-card-btn:hover {
  background: #6c757d;
  color: white;
}

.answer-card-btn i {
  font-size: 16px;
  margin-bottom: 2px;
}

.answer-card-btn span {
  font-size: 10px;
  font-weight: bold;
}

/* Modal 中的答題卡樣式 */
#answerCardModal .answer-card {
  position: static;
  top: auto;
}

#answerCardModal .card {
  border: none;
  box-shadow: none;
}

#answerCardModal .card-header {
  display: none;
  /* 隱藏標題，因為 Modal 已有標題 */
}

#answerCardModal .card-body {
  padding: 20px;
}

@media (max-width: 768px) {
  .exam-card {
    padding: 30px 20px;
  }

  .option-item {
    padding: 10px 15px;
  }

  .fw-semibold {
    font-size: 1rem;
  }
}
</style>
