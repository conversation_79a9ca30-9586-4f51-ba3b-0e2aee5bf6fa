<!DOCTYPE html>
<html>
<head>
    <title>Firebase 連接測試</title>
</head>
<body>
    <h1>Firebase 連接測試</h1>
    <button onclick="testConnection()">測試連接</button>
    <button onclick="testAuth()">測試認證</button>
    <div id="result"></div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js'
        import { getDatabase, ref, set, get } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-database.js'
        import { getAuth, signInWithEmailAndPassword } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js'

        const firebaseConfig = {
            apiKey: 'AIzaSyBkue5fQAVIZ4gmaYKdgu_NsfrfmubpvMo',
            authDomain: 'exam-proj-aea00.firebaseapp.com',
            databaseURL: 'https://exam-proj-aea00-default-rtdb.asia-southeast1.firebasedatabase.app',
            projectId: 'exam-proj-aea00',
            storageBucket: 'exam-proj-aea00.firebasestorage.app',
            messagingSenderId: '245448407969',
            appId: '1:245448407969:web:04c1ef61abd9bb100169e0',
        }

        const app = initializeApp(firebaseConfig)
        const database = getDatabase(app)
        const auth = getAuth(app)

        window.testConnection = async function() {
            const resultDiv = document.getElementById('result')
            try {
                console.log('測試 Firebase 連接...')
                const testRef = ref(database, 'test')
                const testData = { timestamp: Date.now(), message: 'connection test' }
                
                await set(testRef, testData)
                console.log('✅ 寫入成功')
                
                const snapshot = await get(testRef)
                console.log('✅ 讀取成功:', snapshot.val())
                
                resultDiv.innerHTML = '<p style="color: green;">✅ Firebase 連接成功!</p>'
            } catch (error) {
                console.error('❌ Firebase 連接失敗:', error)
                resultDiv.innerHTML = `<p style="color: red;">❌ Firebase 連接失敗: ${error.message}</p>`
            }
        }

        window.testAuth = async function() {
            const resultDiv = document.getElementById('result')
            try {
                // 使用測試帳號登入
                const email = prompt('請輸入測試用的電子郵件:')
                const password = prompt('請輸入密碼:')
                
                if (!email || !password) {
                    resultDiv.innerHTML = '<p style="color: orange;">取消測試</p>'
                    return
                }
                
                const userCredential = await signInWithEmailAndPassword(auth, email, password)
                const user = userCredential.user
                console.log('✅ 登入成功:', user)
                
                // 測試寫入用戶資料
                const userRef = ref(database, `userAnswers/${user.uid}/test`)
                const userData = { 
                    timestamp: Date.now(), 
                    message: 'auth test',
                    email: user.email 
                }
                
                await set(userRef, userData)
                console.log('✅ 用戶資料寫入成功')
                
                resultDiv.innerHTML = `<p style="color: green;">✅ 認證和寫入都成功!<br>用戶 UID: ${user.uid}</p>`
            } catch (error) {
                console.error('❌ 認證測試失敗:', error)
                resultDiv.innerHTML = `<p style="color: red;">❌ 認證測試失敗: ${error.message}</p>`
            }
        }
    </script>
</body>
</html>
