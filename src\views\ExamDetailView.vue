<template>
  <div>
    <div class="container-fluid">
      <div class="row">
        <!-- 答題卡側邊欄 -->
        <div class="col-lg-3">
          <AnswerCard v-if="examDetail" :questions="examDetail.questions" :user-answers="examDetail.userAnswers"
            :current-index="currentQuestionIndex" :correct-answers="examDetail.questions.map(q => q.answer)"
            mode="review" @jump-to-question="jumpToQuestion" />
        </div>

        <!-- 主要內容區域 -->
        <div class="col-lg-9">
          <div class="exam-card h-100">
            <!-- 返回按鈕 - 只在從歷史記錄進入時顯示 -->
            <div v-if="isFromHistory" class="text-center mb-4">
              <BackBtn text="返回歷史記錄" @click="goBack" />
            </div>

            <div v-if="!examDetail" class="text-center py-5">
              <h4 class="text-muted">找不到考試記錄</h4>
              <router-link to="/history" class="btn btn-gradient">返回歷史記錄</router-link>
            </div>

            <div v-else>
              <!-- 考試資訊卡片 -->
              <div class="card text-white mb-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="card-body">
                  <div class="row align-items-center ">
                    <div class="col-md-8">
                      <h4 class="mb-2">
                        <i class="fas fa-clipboard-check me-2"></i>
                        {{ examDetail.languageText }}{{ examDetail.chapter.title }}
                      </h4>
                      <div class="d-flex gap-3 flex-wrap">
                        <span>
                          <i class="fas fa-calendar me-1"></i>
                          {{ examDetail.date }}
                        </span>
                      </div>
                    </div>
                    <div class="col-md-4 text-end">
                      <span class="display-3 fw-bold">{{ examDetail.score }}分</span>
                      <div>
                        <span class="badge bg-light text-dark fs-6 p-2">
                          ✔️ {{ examDetail.correctCount }}/{{ examDetail.totalQuestions }} 題
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 答題回顧標題 -->
              <h3 class="text-center mb-4">
                <i class="fas fa-search"></i>
                答題回顧
              </h3>

              <!-- 全部答對的情況 -->
              <div v-if="examDetail.incorrectQuestions.length === 0" class="alert alert-success text-center p-4">
                <i class="fas fa-trophy fs-1 mb-3 d-block"></i>
                <h4 class="fw-bold mb-2">🎊 恭喜！全部答對！</h4>
                <p class="mb-0">您的表現非常出色！</p>
              </div>

              <!-- 題目回顧 -->
              <div v-else class="d-grid gap-4" ref="questionsContainer">
                <div v-for="(question, index) in examDetail.questions" :key="index" class="question-review-card"
                  :data-question-index="index">
                  <!-- 題目內容 -->
                  <div class="card border-0 bg-light">
                    <div class="card-body">
                      <!-- 答題狀態 -->
                      <div class="d-flex align-items-center justify-content-between mb-3">
                        <div class="d-flex align-items-center gap-2">
                          <span v-if="isAnswerCorrect(examDetail.userAnswers[index], question.answer)"
                            class="badge bg-success fs-6">
                            <i class="fas fa-check me-1"></i>
                            答對
                          </span>
                          <span v-else class="badge bg-danger fs-6">
                            <i class="fas fa-times me-1"></i>
                            答錯
                          </span>
                        </div>
                        <div class="text-muted">
                          <small>
                            我的答案：
                            <span v-if="examDetail.userAnswers[index] === -1 || examDetail.userAnswers[index] === null">
                              未作答
                            </span>
                            <span v-else-if="Array.isArray(examDetail.userAnswers[index])">
                              {{examDetail.userAnswers[index].map(ans => String.fromCharCode(65 + ans)).join(', ')}}
                            </span>
                            <span v-else>
                              {{ String.fromCharCode(65 + examDetail.userAnswers[index]) }}
                            </span>
                          </small>
                        </div>
                      </div>

                      <h5 class="card-title mb-4">
                        {{ index + 1 }}. {{ question.q }}
                      </h5>

                      <!-- 選項 -->
                      <div class="d-grid gap-2">
                        <div v-for="(option, optionIndex) in question.options" :key="optionIndex" class="option-review"
                          :class="{
                            'user-answer': examDetail.userAnswers[index] === optionIndex || (Array.isArray(examDetail.userAnswers[index]) && examDetail.userAnswers[index].includes(optionIndex))
                          }">
                          <div class="option-letter">{{ String.fromCharCode(65 + optionIndex) }}</div>
                          <div class="flex-grow-1">{{ option }}</div>
                        </div>
                      </div>

                      <!-- 正確答案區域 -->
                      <div class="correct-answer-section mt-3 pt-3 border-top">
                        <div class="correct-answer">
                          <strong>正確答案：</strong>
                          <span v-if="Array.isArray(question.answer)">
                            {{question.answer.map(ans => String.fromCharCode(65 + ans)).join(', ')}}
                          </span>
                          <span v-else>
                            {{ String.fromCharCode(65 + question.answer) }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 操作按鈕區域 -->
            <div class="d-flex gap-3 justify-content-center mt-4 flex-wrap">
              <button class="btn btn-secondary d-flex align-items-center gap-2" @click="retakeExam">
                <i class="fas fa-redo"></i>
                重新測驗
              </button>
              <!-- 考完試時顯示返回章節選擇按鈕 -->
              <button v-if="!isFromHistory" class="btn btn-gradient d-flex align-items-center gap-2"
                @click="goToChapters">
                <i class="fas fa-list"></i>
                返回章節選擇
              </button>
              <button class="btn btn-info d-flex align-items-center gap-2" @click="goToHistory">
                <i class="fas fa-history"></i>
                查看歷史記錄
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, ref, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useExamStore } from '../stores/examStore'
import AnswerCard from '../components/AnswerCard.vue'
import BackBtn from '../components/BackBtn.vue'

const router = useRouter()
const route = useRoute()
const examStore = useExamStore()

// 檢查登入狀態
if (!examStore.user.isLoggedIn) {
  router.push('/')
}

const currentQuestionIndex = ref(0)
const questionsContainer = ref(null)
let scrollTimeout = null

// 判斷是否來自歷史記錄頁面
// 如果 URL 包含 from=history 查詢參數，或者 referrer 是歷史頁面，則認為是從歷史記錄進入
const isFromHistory = computed(() => {
  return route.query.from === 'history' ||
    (typeof document !== 'undefined' && document.referrer.includes('/history'))
})

const examDetail = computed(() => {
  const examId = route.params.id
  console.log('ExamDetailView - 查找考試記錄，ID:', examId)
  console.log('ExamDetailView - 當前歷史記錄:', examStore.examHistory)

  // 嘗試字符串匹配和數字匹配
  const found = examStore.examHistory.find(exam =>
    exam.id === examId ||
    exam.id === parseInt(examId) ||
    exam.id.toString() === examId
  )

  console.log('ExamDetailView - 找到的記錄:', found)
  return found
})

// 滾動監聽函數 - 檢測當前可見的題目
const handleScroll = () => {
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }

  scrollTimeout = setTimeout(() => {
    const questionCards = document.querySelectorAll('.question-review-card')
    if (!questionCards.length) return

    const viewportHeight = window.innerHeight
    const scrollTop = window.scrollY
    const viewportCenter = scrollTop + viewportHeight / 2

    let closestIndex = 0
    let closestDistance = Infinity

    questionCards.forEach((card, index) => {
      const rect = card.getBoundingClientRect()
      const cardTop = rect.top + scrollTop
      const cardCenter = cardTop + rect.height / 2
      const distance = Math.abs(cardCenter - viewportCenter)

      if (distance < closestDistance) {
        closestDistance = distance
        closestIndex = index
      }
    })

    // 只有當索引真正改變時才更新
    if (currentQuestionIndex.value !== closestIndex) {
      currentQuestionIndex.value = closestIndex
    }
  }, 100) // 100ms 的防抖延遲
}

const jumpToQuestion = (index) => {
  currentQuestionIndex.value = index
  // 滾動到對應題目
  const element = document.querySelector(`.question-review-card:nth-child(${index + 1})`)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

const isAnswerCorrect = (userAnswer, correctAnswer) => {
  // 處理複選題
  if (Array.isArray(correctAnswer)) {
    if (!Array.isArray(userAnswer)) return false
    if (userAnswer.length !== correctAnswer.length) return false
    return userAnswer.every(ans => correctAnswer.includes(ans)) &&
      correctAnswer.every(ans => userAnswer.includes(ans))
  }
  // 處理單選題
  return userAnswer === correctAnswer
}

const retakeExam = async () => {
  if (examDetail.value?.chapter) {
    const success = await examStore.startExam(examDetail.value.chapter)
    if (success) {
      router.push(`/exam/${examDetail.value.chapter.id}`)
    }
  }
}

const goToChapters = () => {
  examStore.resetExam()
  examStore.selectLanguage(examDetail.value?.language || examStore.currentLanguage)
  router.push('/chapters')
}

const goToHistory = () => {
  router.push('/history')
}

const goBack = () => {
  router.push('/history')
}

onMounted(() => {
  console.log('ExamDetailView - onMounted 開始')
  console.log('ExamDetailView - examDetail:', examDetail.value)

  if (!examDetail.value) {
    console.log('ExamDetailView - 沒有找到考試記錄，跳轉到歷史頁面')
    router.push('/history')
    return
  }

  // 添加滾動監聽器
  window.addEventListener('scroll', handleScroll, { passive: true })

  // 初始化當前題目索引
  nextTick(() => {
    handleScroll()
  })
})

onUnmounted(() => {
  // 清理滾動監聽器和定時器
  window.removeEventListener('scroll', handleScroll)
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }
})
</script>

<style scoped>
.question-review-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.option-review {
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  padding: 15px 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
}

.option-review.user-answer {
  border-color: #17a2b8;
  background: #d1ecf1;
  color: #0c5460;
}

.option-letter {
  font-weight: bold;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #e1e5e9;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.option-review.user-answer .option-letter {
  background: #17a2b8;
  color: white;
}

.correct-answer-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
}

.correct-answer {
  color: #495057;
  font-size: 0.95rem;
}

.option-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-gradient {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  color: white;
}

.btn-gradient:hover {
  background: linear-gradient(45deg, #5a6fd8, #6a4190);
  color: white;
}

.display-3 {
  font-size: 3rem !important;
}

@media (max-width: 768px) {
  .question-review-card {
    padding: 20px 15px;
  }

  .option-review {
    padding: 12px 15px;
  }

  .display-3 {
    font-size: 2rem !important;
  }

  .d-flex.gap-3 {
    flex-direction: column;
  }
}
</style>
