<template>
  <div>
    <div class="col-lg-6 mx-auto d-flex">
      <div class="exam-card large">
        <!-- 返回按鈕 -->
        <BackBtn text="選擇模式" @click="goBack" />

        <div class="text-center mb-4">
          <h1 class="display-4 gradient-text fw-bold mb-3">{{ pageTitle }}</h1>
          <p class="text-muted fs-5">{{ languageTitle }}</p>
        </div>

        <div v-if="loading" class="text-center py-5">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">載入中...</span>
          </div>
          <p class="mt-3 text-muted">載入章節資料中...</p>
        </div>

        <div v-else-if="error" class="alert alert-danger">
          <i class="fas fa-exclamation-triangle me-2"></i>
          {{ error }}
        </div>

        <div v-else class="row g-3 mb-4">
          <div v-for="chapter in chapters" :key="chapter.id" class="col-lg-4 col-md-6">
            <div class="chapter-card h-100" :class="{ 'question-bank-card': examStore.currentMode === 'questionBank' }"
              @click="handleChapterClick(chapter)">
              <div class="text-center custom-flex-sm">
                <div class="title gradient-text">{{ chapter.title }}</div>
                <div class="fw-bold fs-5">
                  <span v-if="examStore.currentMode === 'exam'">✏️</span>
                  <span v-else>📖</span>
                  {{ chapter.questionCount || 0 }}題
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useExamStore } from '../stores/examStore'
import BackBtn from '../components/BackBtn.vue'
import { ref as dbRef, get } from 'firebase/database'
import { database } from '../firebase/config'

const router = useRouter()
const examStore = useExamStore()

const chapters = ref([])
const loading = ref(true)
const error = ref('')

// 檢查登入狀態和語言選擇
if (!examStore.user.isLoggedIn) {
  router.push('/')
} else if (!examStore.currentLanguage) {
  router.push('/language')
}

const pageTitle = computed(() => {
  if (examStore.currentMode === 'questionBank') {
    return '選擇題庫章節'
  } else {
    return '選擇測驗章節'
  }
})

const languageTitle = computed(() => {
  const modeText = examStore.currentMode === 'questionBank' ? '題庫' : '測驗'
  if (examStore.currentLanguage === '中文') {
    return `中文${modeText}章節`
  } else if (examStore.currentLanguage === '英文') {
    return `英文${modeText}章節`
  } else {
    return `${examStore.currentLanguage} ${modeText}章節`
  }
})


// 載入章節資料
const loadChapters = async () => {
  loading.value = true
  error.value = ''

  try {
    // 使用 examStore.currentLanguage
    const languageKey = examStore.currentLanguage

    const chineseRef = dbRef(database, languageKey)
    const snapshot = await get(chineseRef)

    if (snapshot.exists()) {
      const data = snapshot.val()
      console.log('loadChapters - 載入的章節資料:', data)

      // 轉換資料格式：第一層 key 是章節名稱，value 是題目陣列
      const chapterList = Object.keys(data).map((chapterName, index) => ({
        id: index + 1,
        title: chapterName, // 直接使用第一層的 key 作為標題
        name: chapterName,  // 保存原始名稱用於後續查詢
        desc: `${chapterName}相關題目`,
        questionCount: data[chapterName].length
      }))

      console.log('loadChapters - 轉換後的章節列表:', chapterList)
      chapters.value = chapterList
    } else {
      console.log('loadChapters - 沒有找到資料')
      error.value = '找不到章節資料'
      chapters.value = []
    }
  } catch (err) {
    console.error('loadChapters - 發生錯誤:', err)
    error.value = '載入章節資料失敗，請重新整理頁面'
    chapters.value = []
  } finally {
    loading.value = false
  }
}

const handleChapterClick = async (chapter) => {
  if (examStore.currentMode === 'exam') {
    // 考試模式：開始考試
    const success = await examStore.startExam(chapter)
    if (success) {
      router.push(`/exam/${chapter.id}`)
    } else {
      error.value = '載入考試題目失敗，請重試'
    }
  } else {
    // 題庫模式：查看題庫
    const success = await examStore.startExam(chapter)
    if (success) {
      router.push(`/questionbank/${chapter.id}`)
    } else {
      error.value = '載入題庫失敗，請重試'
    }
  }
}

const goBack = () => {
  router.push('/language')
}

// 監聽語言變化，當語言改變時重新載入章節
watch(() => examStore.currentLanguage, (newLanguage, oldLanguage) => {
  if (newLanguage && newLanguage !== oldLanguage) {
    loadChapters()
  }
}, { immediate: false })

onMounted(() => {
  loadChapters()
})
</script>

<style scoped>
.title {
  font-size: 2rem;
  font-weight: bold;
}

.question-bank-card:hover {
  background-image: linear-gradient(to top, #c1dfc4 0%, #deecdd 100%);
  transform: translateY(-2px);
  border: 2px solid #a3caa7;
}


@media (max-width: 768px) {
  .exam-card {
    padding: 30px 20px;
  }

  .display-4 {
    font-size: 2rem !important;
  }

  .chapter-card {
    padding: 10px;
  }
}

@media (max-width: 768px) {
  .custom-flex-sm {
    display: flex;
    gap: 0.5rem;
    /* 等於 gap-2 */
    justify-content: center;
    align-items: center;
    text-align: center;
  }
}
</style>
