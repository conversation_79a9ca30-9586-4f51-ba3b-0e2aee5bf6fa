import { defineStore } from 'pinia'
import { getQuestions, getChapters, saveUserAnswers, getUserAnswers } from '../firebase/database'
import { logoutUser, onAuthStateChange } from '../firebase/auth'
import { setCookie, getCookie, deleteCookie } from '../utils/cookies'

export const useExamStore = defineStore('exam', {
  state: () => ({
    // 使用者資訊
    user: {
      uid: '',
      email: '',
      isLoggedIn: false,
    },

    // 考試設定
    currentLanguage: '',
    currentChapter: null,
    currentQuestionIndex: 0,
    currentMode: 'exam', // 'exam' 或 'questionBank'

    // 考試資料
    questions: [],
    userAnswers: [],

    // 考試結果
    score: 0,
    correctCount: 0,
    incorrectQuestions: [],

    // 考試紀錄
    examHistory: [],
    selectedExamHistory: null,

    // 題目資料
    questionData: {
      chinese: {
        chapters: [
          {
            id: 1,
            title: '基礎語法',
            desc: '基本語法概念與應用',
            questions: [
              {
                q: '下列哪個是正確的句子？',
                options: ['他很高興。', '他很高興的。', '他很高興了。', '他很高興呢。'],
                answer: 0,
              },
              {
                q: '「的」和「得」的用法，下列何者正確？',
                options: ['他跑得很快', '他跑的很快', '美麗的花', '美麗得花'],
                answer: 0,
              },
              { q: '下列哪個詞語是動詞？', options: ['美麗', '跑步', '高興', '桌子'], answer: 1 },
              {
                q: '「因為...所以...」的用法，下列何者正確？',
                options: [
                  '因為下雨，所以我帶傘',
                  '因為下雨，我帶傘',
                  '下雨，所以我帶傘',
                  '因為下雨所以我帶傘',
                ],
                answer: 0,
              },
              {
                q: '下列哪個是疑問句？',
                options: ['今天天氣很好。', '今天天氣好嗎？', '今天天氣真好！', '今天天氣好。'],
                answer: 1,
              },
              {
                q: '下列哪些是正確的標點符號使用？（複選題）',
                options: ['你好嗎？', '他很高興。', '太棒了！', '我去學校，'],
                answer: [0, 1, 2],
                type: 'multiple',
              },
            ],
          },
          {
            id: 2,
            title: '詞彙應用',
            desc: '常用詞彙的正確使用',
            questions: [
              { q: '「學習」的同義詞是？', options: ['教導', '研讀', '遊戲', '休息'], answer: 1 },
              {
                q: '下列哪個詞語表示「非常」的意思？',
                options: ['有點', '稍微', '極其', '還算'],
                answer: 2,
              },
              { q: '「高興」的反義詞是？', options: ['快樂', '難過', '興奮', '滿足'], answer: 1 },
              {
                q: '下列哪個詞語可以形容天氣？',
                options: ['聰明', '晴朗', '勤奮', '友善'],
                answer: 1,
              },
              {
                q: '「朋友」的量詞是？',
                options: ['一個朋友', '一位朋友', '一名朋友', '以上皆可'],
                answer: 3,
              },
              {
                q: '下列哪些詞語可以形容人的性格？（複選題）',
                options: ['善良', '桌子', '勤奮', '友善'],
                answer: [0, 2, 3],
                type: 'multiple',
              },
            ],
          },
          {
            id: 3,
            title: '閱讀理解',
            desc: '短文閱讀與理解能力',
            questions: [
              {
                q: '小明每天早上七點起床，八點上學。請問小明幾點起床？',
                options: ['六點', '七點', '八點', '九點'],
                answer: 1,
              },
              {
                q: '「春天來了，花兒開了，鳥兒唱歌了。」這句話描述的是什麼季節？',
                options: ['春天', '夏天', '秋天', '冬天'],
                answer: 0,
              },
              {
                q: '根據文章「媽媽買了蘋果、香蕉和橘子」，媽媽買了幾種水果？',
                options: ['一種', '兩種', '三種', '四種'],
                answer: 2,
              },
              {
                q: '「圖書館很安靜，大家都在認真讀書。」這裡的「安靜」是什麼意思？',
                options: ['吵鬧', '沒有聲音', '很熱鬧', '很忙碌'],
                answer: 1,
              },
              {
                q: '「他走得很慢，但是很穩。」這句話說明了什麼？',
                options: ['他走得快', '他走得慢而穩', '他不會走路', '他在跑步'],
                answer: 1,
              },
            ],
          },
          {
            id: 4,
            title: '寫作基礎',
            desc: '基本寫作技巧與結構',
            questions: [
              {
                q: '一篇好的文章應該包含什麼？',
                options: ['只有開頭', '開頭、內容、結尾', '只有內容', '只有結尾'],
                answer: 1,
              },
              {
                q: '寫作時，段落之間應該如何連接？',
                options: ['直接連接', '用轉折詞', '不用連接', '隨意連接'],
                answer: 1,
              },
              {
                q: '下列哪個是好的開頭方式？',
                options: ['直接結論', '提出問題', '列舉事實', '以上皆可'],
                answer: 3,
              },
              {
                q: '寫作時應該注意什麼？',
                options: ['只注意內容', '只注意格式', '內容和格式都重要', '都不重要'],
                answer: 2,
              },
              {
                q: '修改文章時，首先應該檢查什麼？',
                options: ['錯字', '標點符號', '內容邏輯', '字數'],
                answer: 2,
              },
            ],
          },
          {
            id: 5,
            title: '文學常識',
            desc: '基本文學知識與作品',
            questions: [
              {
                q: '下列哪位是著名的中國古代詩人？',
                options: ['李白', '牛頓', '愛因斯坦', '莎士比亞'],
                answer: 0,
              },
              {
                q: '「靜夜思」是誰的作品？',
                options: ['杜甫', '李白', '白居易', '王維'],
                answer: 1,
              },
              {
                q: '中國古代四大名著不包括哪一部？',
                options: ['西遊記', '水滸傳', '三國演義', '聊齋誌異'],
                answer: 3,
              },
              {
                q: '「詩聖」指的是哪位詩人？',
                options: ['李白', '杜甫', '白居易', '王維'],
                answer: 1,
              },
              {
                q: '下列哪個是中國傳統節日？',
                options: ['聖誕節', '中秋節', '萬聖節', '復活節'],
                answer: 1,
              },
            ],
          },
          {
            id: 6,
            title: '綜合測驗',
            desc: '綜合各項語文能力',
            questions: [
              {
                q: '「書中自有黃金屋」這句話的意思是？',
                options: ['書裡有金子', '讀書能獲得知識財富', '書很貴重', '書是黃金做的'],
                answer: 1,
              },
              {
                q: '下列哪個句子使用了比喻的修辭技巧？',
                options: ['他很高', '他像山一樣高', '他比我高', '他最高'],
                answer: 1,
              },
              {
                q: '「一寸光陰一寸金」告訴我們什麼道理？',
                options: ['金子很珍貴', '時間很珍貴', '光陰似箭', '寸金難買'],
                answer: 1,
              },
              {
                q: '下列哪個是正確的標點符號使用？',
                options: ['你好嗎。', '你好嗎？', '你好嗎！', '你好嗎，'],
                answer: 1,
              },
              {
                q: '「學而時習之，不亦說乎？」出自哪本書？',
                options: ['論語', '孟子', '老子', '莊子'],
                answer: 0,
              },
            ],
          },
        ],
      },
      english: {
        chapters: [
          {
            id: 1,
            title: 'Basic Grammar',
            desc: 'Fundamental grammar concepts',
            questions: [
              {
                q: 'Which sentence is correct?',
                options: [
                  'He is very happy.',
                  'He very happy.',
                  'He is very happily.',
                  'He very happily.',
                ],
                answer: 0,
              },
              {
                q: 'Choose the correct form of "be":',
                options: ['I are student', 'I am student', 'I am a student', 'I are a student'],
                answer: 2,
              },
              {
                q: 'What is the past tense of "go"?',
                options: ['goed', 'went', 'gone', 'going'],
                answer: 1,
              },
              {
                q: 'Which is a verb?',
                options: ['beautiful', 'run', 'happiness', 'table'],
                answer: 1,
              },
              {
                q: 'Choose the correct question:',
                options: [
                  'What your name?',
                  'What is your name?',
                  'What your name is?',
                  'Your name what is?',
                ],
                answer: 1,
              },
              {
                q: 'Which of the following are correct sentences? (Multiple choice)',
                options: ['I am happy.', 'She are student.', 'They are friends.', 'He is doctor.'],
                answer: [0, 2],
                type: 'multiple',
              },
            ],
          },
          {
            id: 2,
            title: 'Vocabulary',
            desc: 'Common vocabulary usage',
            questions: [
              {
                q: 'What is a synonym for "happy"?',
                options: ['sad', 'angry', 'joyful', 'tired'],
                answer: 2,
              },
              {
                q: 'Which word means "very big"?',
                options: ['tiny', 'huge', 'small', 'little'],
                answer: 1,
              },
              {
                q: 'What is the opposite of "hot"?',
                options: ['warm', 'cool', 'cold', 'freezing'],
                answer: 2,
              },
              {
                q: 'Which word describes weather?',
                options: ['intelligent', 'sunny', 'friendly', 'delicious'],
                answer: 1,
              },
              {
                q: 'What do you call a person who teaches?',
                options: ['student', 'teacher', 'doctor', 'driver'],
                answer: 1,
              },
            ],
          },
          {
            id: 3,
            title: 'Reading',
            desc: 'Reading comprehension skills',
            questions: [
              {
                q: 'Tom wakes up at 7 AM and goes to school at 8 AM. What time does Tom wake up?',
                options: ['6 AM', '7 AM', '8 AM', '9 AM'],
                answer: 1,
              },
              {
                q: '"Spring is here, flowers bloom, birds sing." What season is described?',
                options: ['Spring', 'Summer', 'Fall', 'Winter'],
                answer: 0,
              },
              {
                q: 'According to "Mom bought apples, bananas, and oranges", how many types of fruit did Mom buy?',
                options: ['one', 'two', 'three', 'four'],
                answer: 2,
              },
              {
                q: '"The library is quiet, everyone is reading seriously." What does "quiet" mean?',
                options: ['noisy', 'silent', 'crowded', 'busy'],
                answer: 1,
              },
              {
                q: '"He walks slowly but steadily." What does this sentence tell us?',
                options: [
                  'He walks fast',
                  'He walks slowly but steadily',
                  'He cannot walk',
                  'He is running',
                ],
                answer: 1,
              },
            ],
          },
          {
            id: 4,
            title: 'Writing',
            desc: 'Basic writing skills',
            questions: [
              {
                q: 'What should a good essay include?',
                options: [
                  'Only introduction',
                  'Introduction, body, conclusion',
                  'Only body',
                  'Only conclusion',
                ],
                answer: 1,
              },
              {
                q: 'How should paragraphs be connected in writing?',
                options: [
                  'Direct connection',
                  'Using transition words',
                  'No connection needed',
                  'Random connection',
                ],
                answer: 1,
              },
              {
                q: 'Which is a good way to start an essay?',
                options: ['Direct conclusion', 'Ask a question', 'State facts', 'All of the above'],
                answer: 3,
              },
              {
                q: 'What should you pay attention to when writing?',
                options: ['Only content', 'Only format', 'Both content and format', 'Neither'],
                answer: 2,
              },
              {
                q: 'When revising an essay, what should you check first?',
                options: ['Spelling', 'Punctuation', 'Logic of content', 'Word count'],
                answer: 2,
              },
            ],
          },
          {
            id: 5,
            title: 'Literature',
            desc: 'Basic literary knowledge',
            questions: [
              {
                q: 'Who wrote "Romeo and Juliet"?',
                options: ['Charles Dickens', 'William Shakespeare', 'Jane Austen', 'Mark Twain'],
                answer: 1,
              },
              {
                q: 'What is a haiku?',
                options: ['A novel', 'A short poem', 'A play', 'A song'],
                answer: 1,
              },
              {
                q: 'Which is NOT a genre of literature?',
                options: ['Poetry', 'Fiction', 'Drama', 'Mathematics'],
                answer: 3,
              },
              {
                q: 'What is the main character in a story called?',
                options: ['Antagonist', 'Protagonist', 'Narrator', 'Author'],
                answer: 1,
              },
              {
                q: 'What is a metaphor?',
                options: [
                  'A direct comparison',
                  'An indirect comparison',
                  'A question',
                  'A statement',
                ],
                answer: 1,
              },
            ],
          },
          {
            id: 6,
            title: 'Comprehensive',
            desc: 'Mixed language skills test',
            questions: [
              {
                q: 'Which sentence uses correct punctuation?',
                options: [
                  'Hello, how are you.',
                  'Hello, how are you?',
                  'Hello how are you?',
                  'Hello, how are you!',
                ],
                answer: 1,
              },
              {
                q: 'What figure of speech is "Life is a journey"?',
                options: ['Simile', 'Metaphor', 'Alliteration', 'Onomatopoeia'],
                answer: 1,
              },
              {
                q: 'Which is a complete sentence?',
                options: ['Running fast', 'The dog', 'She runs fast', 'Very quickly'],
                answer: 2,
              },
              {
                q: 'What is the correct order for an essay?',
                options: [
                  'Conclusion, body, introduction',
                  'Body, introduction, conclusion',
                  'Introduction, body, conclusion',
                  'Introduction, conclusion, body',
                ],
                answer: 2,
              },
              {
                q: 'Which shows correct subject-verb agreement?',
                options: ['The dogs runs', 'The dog run', 'The dogs run', 'The dog are running'],
                answer: 2,
              },
            ],
          },
        ],
      },
    },
  }),

  getters: {
    getCurrentChapterData: (state) => {
      if (!state.currentLanguage || !state.currentChapter) return null
      return state.questionData[state.currentLanguage].chapters.find(
        (ch) => ch.id === state.currentChapter.id,
      )
    },

    getCurrentQuestion: (state) => {
      if (state.questions.length === 0) return null
      return state.questions[state.currentQuestionIndex]
    },

    getChaptersByLanguage: (state) => (language) => {
      return state.questionData[language]?.chapters || []
    },

    isLastQuestion: (state) => {
      return state.currentQuestionIndex === state.questions.length - 1
    },

    getPerformanceMessage: (state) => {
      if (state.score >= 90) return '🎉 優秀！您的表現非常出色！'
      if (state.score >= 80) return '👍 良好！繼續保持努力！'
      if (state.score >= 60) return '📚 還需努力，建議多加練習！'
      return '💪 加油！建議重新學習相關內容！'
    },
  },

  actions: {
    // 設置用戶資訊 (Firebase 登入後調用)
    setUser(firebaseUser) {
      console.log('examStore - 設置用戶資訊:', firebaseUser)
      this.user.uid = firebaseUser.uid
      this.user.email = firebaseUser.email
      this.user.isLoggedIn = true
      console.log('examStore - 用戶狀態更新後:', this.user)

      // 將登入狀態存入 Cookie
      setCookie('userLoggedIn', 'true', 7) // 7天有效期
      setCookie('userEmail', firebaseUser.email, 7)
      setCookie('userUid', firebaseUser.uid, 7)

      this.loadExamHistory()
    },

    // 登出
    async logout() {
      try {
        await logoutUser()
        this.user.uid = ''
        this.user.email = ''
        this.user.isLoggedIn = false

        // 清除 Cookie
        deleteCookie('userLoggedIn')
        deleteCookie('userEmail')
        deleteCookie('userUid')

        this.resetExam()
      } catch (error) {
        console.error('登出失敗:', error)
      }
    },

    // 初始化認證狀態監聽
    initAuth() {
      return onAuthStateChange((user) => {
        if (user) {
          this.setUser(user)
        } else {
          // 檢查 Cookie 中是否有登入狀態
          const isLoggedInFromCookie = getCookie('userLoggedIn')
          const emailFromCookie = getCookie('userEmail')
          const uidFromCookie = getCookie('userUid')

          if (isLoggedInFromCookie === 'true' && emailFromCookie && uidFromCookie) {
            // 從 Cookie 恢復登入狀態
            this.user.uid = uidFromCookie
            this.user.email = emailFromCookie
            this.user.isLoggedIn = true
            this.loadExamHistory()
          } else {
            this.user.uid = ''
            this.user.email = ''
            this.user.isLoggedIn = false
          }
        }
      })
    },

    // 從 Cookie 檢查並恢復登入狀態
    checkLoginFromCookie() {
      const isLoggedInFromCookie = getCookie('userLoggedIn')
      const emailFromCookie = getCookie('userEmail')
      const uidFromCookie = getCookie('userUid')

      console.log('examStore - 檢查 Cookie 登入狀態:', {
        isLoggedIn: isLoggedInFromCookie,
        email: emailFromCookie,
        uid: uidFromCookie,
      })

      if (isLoggedInFromCookie === 'true' && emailFromCookie && uidFromCookie) {
        this.user.uid = uidFromCookie
        this.user.email = emailFromCookie
        this.user.isLoggedIn = true
        console.log('examStore - 從 Cookie 恢復用戶狀態:', this.user)
        this.loadExamHistory()
        return true
      }
      console.log('examStore - Cookie 中沒有有效的登入狀態')
      return false
    },

    // 選擇語言
    selectLanguage(language) {
      this.currentLanguage = language
    },

    // 設置模式
    setMode(mode) {
      this.currentMode = mode
    },

    // 載入章節列表
    async loadChapters(language) {
      try {
        const result = await getChapters(language)
        if (result.success) {
          return result.data
        } else {
          console.error('載入章節失敗:', result.error)
          return []
        }
      } catch (error) {
        console.error('載入章節錯誤:', error)
        return []
      }
    },

    // 開始考試 (從 Firebase 載入題目)
    async startExam(chapter) {
      try {
        console.log('開始考試，章節資訊:', chapter)

        // 從 Firebase 載入題目
        const result = await getQuestions(this.currentLanguage, chapter.name)

        if (result.success && result.data) {
          this.currentChapter = chapter
          console.log('原始題目資料:', result.data)

          // 轉換 Firebase 資料格式為應用程式格式
          this.questions = result.data.map((item, index) => {
            console.log(`轉換題目 ${index + 1}:`, item)

            // 處理答案格式
            let answer
            if (item.是否複選) {
              // 複選題：將正確答案代號轉換為選項索引陣列
              answer = item.正確答案.map((ans) => item.選項.findIndex((opt) => opt.代號 === ans))
            } else {
              // 單選題：將正確答案代號轉換為選項索引
              answer = item.選項.findIndex((opt) => opt.代號 === item.正確答案[0])
            }

            const convertedQuestion = {
              q: item.題目,
              options: item.選項.map((opt) => opt.內容),
              answer: answer,
              type: item.是否複選 ? 'multiple' : 'single',
            }

            console.log(`轉換後的題目 ${index + 1}:`, convertedQuestion)
            return convertedQuestion
          })

          console.log('所有轉換後的題目:', this.questions)

          this.currentQuestionIndex = 0
          this.userAnswers = this.questions.map((q) => (q.type === 'multiple' ? [] : -1))

          console.log('初始化用戶答案:', this.userAnswers)
          return true
        } else {
          console.error('載入題目失敗:', result.error)
          return false
        }
      } catch (error) {
        console.error('載入題目錯誤:', error)
        return false
      }
    },

    // 選擇答案
    selectAnswer(answerIndex) {
      this.userAnswers[this.currentQuestionIndex] = answerIndex
    },

    // 下一題
    nextQuestion() {
      if (this.currentQuestionIndex < this.questions.length - 1) {
        this.currentQuestionIndex++
      }
    },

    // 上一題
    prevQuestion() {
      if (this.currentQuestionIndex > 0) {
        this.currentQuestionIndex--
      }
    },

    // 計算結果
    async calculateResults() {
      this.correctCount = 0
      this.incorrectQuestions = []

      this.userAnswers.forEach((answer, index) => {
        const question = this.questions[index]
        const correctAnswer = question.answer

        let isCorrect = false

        // 處理複選題
        if (Array.isArray(correctAnswer)) {
          if (Array.isArray(answer) && answer.length === correctAnswer.length) {
            isCorrect =
              answer.every((ans) => correctAnswer.includes(ans)) &&
              correctAnswer.every((ans) => answer.includes(ans))
          }
        } else {
          // 處理單選題
          isCorrect = answer === correctAnswer
        }

        if (isCorrect) {
          this.correctCount++
        } else {
          this.incorrectQuestions.push({
            questionIndex: index,
            question: question,
            userAnswer: answer,
            correctAnswer: correctAnswer,
          })
        }
      })

      this.score = Math.round((this.correctCount / this.questions.length) * 100)

      // 保存考試紀錄並返回新記錄的 ID
      const newRecordId = await this.saveExamHistory()
      return newRecordId
    },

    // 保存考試紀錄 (到 Firebase)
    async saveExamHistory() {
      console.log('=== 開始保存考試紀錄 ===')
      console.log('完整用戶資訊:', this.user)
      console.log('用戶 UID:', this.user.uid)
      console.log('用戶 email:', this.user.email)
      console.log('用戶登入狀態:', this.user.isLoggedIn)

      if (!this.user.uid) {
        console.error('❌ 用戶 UID 為空，無法保存考試紀錄')
        console.error('請檢查用戶是否正確登入')
        return null
      }

      if (!this.user.isLoggedIn) {
        console.error('❌ 用戶未登入，無法保存考試紀錄')
        return null
      }

      const historyItem = {
        language: this.currentLanguage,
        languageText:
          this.currentLanguage === '中文'
            ? '中文'
            : this.currentLanguage === 'English'
              ? 'English'
              : this.currentLanguage,
        chapter: this.currentChapter,
        score: this.score,
        correctCount: this.correctCount,
        totalQuestions: this.questions.length,
        incorrectQuestions: [...this.incorrectQuestions],
        questions: [...this.questions],
        userAnswers: [...this.userAnswers],
      }

      console.log('準備保存的考試紀錄:', historyItem)

      try {
        console.log('調用 saveUserAnswers...')
        const result = await saveUserAnswers(this.user.uid, historyItem)
        console.log('saveUserAnswers 結果:', result)

        if (result.success) {
          // 同時更新本地歷史記錄
          const localHistoryItem = {
            id: result.id,
            date: new Date().toLocaleString('zh-TW'),
            ...historyItem,
          }
          this.examHistory.unshift(localHistoryItem)
          console.log('本地歷史記錄已更新，新記錄 ID:', result.id)

          // 只保留最近 50 次考試記錄
          if (this.examHistory.length > 50) {
            this.examHistory = this.examHistory.slice(0, 50)
          }

          // 返回新記錄的 ID，供跳轉使用
          return result.id
        } else {
          console.error('保存考試紀錄失敗:', result.error)
          return null
        }
      } catch (error) {
        console.error('保存考試紀錄錯誤:', error)
        return null
      }
    },

    // 載入考試紀錄 (從 Firebase)
    async loadExamHistory() {
      if (!this.user.uid) return

      try {
        const result = await getUserAnswers(this.user.uid)
        if (result.success) {
          this.examHistory = result.data
        } else {
          console.error('載入考試紀錄失敗:', result.error)
        }
      } catch (error) {
        console.error('載入考試紀錄錯誤:', error)
      }
    },

    // 重置考試
    resetExam() {
      this.currentLanguage = ''
      this.currentChapter = null
      this.currentQuestionIndex = 0
      this.currentMode = 'exam'
      this.questions = []
      this.userAnswers = []
      this.score = 0
      this.correctCount = 0
      this.incorrectQuestions = []
    },
  },
})
