<template>
  <div class="answer-card">
    <div class="card">
      <div class="card-header bg-light">
        <h6 class="mb-0">
          <i class="fas fa-th-large me-2"></i>
          答題卡
        </h6>
      </div>
      <div class="card-body p-3">
        <!-- 章節標題顯示 -->
        <div v-if="chapterTitle" class="text-center mb-3">
          <small class="text-muted fw-bold">{{ getChapterDisplayTitle }}</small>
        </div>

        <div class="answer-grid">
          <button v-for="(_, index) in questions" :key="index" class="answer-btn" :class="getButtonClass(index)"
            @click="jumpToQuestion(index)">
            {{ index + 1 }}
          </button>
        </div>

        <!-- 圖例 -->
        <div class="legend mt-3">
          <div v-if="mode === 'exam'" class="legend-items">
            <div class="legend-item">
              <span class="legend-color answered"></span>
              <small>已答</small>
            </div>
            <div class="legend-item">
              <span class="legend-color unanswered"></span>
              <small>未答</small>
            </div>
            <div class="legend-item">
              <span class="legend-color current"></span>
              <small>當前</small>
            </div>
            <div class="legend-item">
              <span class="legend-color marked"></span>
              <small>標記</small>
            </div>
          </div>
          <div v-else-if="mode === 'review'" class="legend-items">
            <div class="legend-item">
              <span class="legend-color correct"></span>
              <small>答對</small>
            </div>
            <div class="legend-item">
              <span class="legend-color incorrect"></span>
              <small>答錯</small>
            </div>
            <div class="legend-item">
              <span class="legend-color current"></span>
              <small>當前</small>
            </div>
          </div>
          <div v-else-if="mode === 'questionBank'" class="legend-items">
            <div class="legend-item">
              <span class="legend-color question-bank"></span>
              <small>題目</small>
            </div>
            <div class="legend-item">
              <span class="legend-color current"></span>
              <small>當前</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  questions: {
    type: Array,
    required: true
  },
  userAnswers: {
    type: Array,
    default: () => []
  },
  currentIndex: {
    type: Number,
    default: 0
  },
  mode: {
    type: String,
    default: 'exam' // 'exam', 'review', or 'questionBank'
  },
  correctAnswers: {
    type: Array,
    default: () => []
  },
  markedQuestions: {
    type: Array,
    default: () => []
  },
  chapterTitle: {
    type: String,
    default: ''
  },
  currentLanguage: {
    type: String,
    default: ''
  },
  isOptionDisabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['jump-to-question', 'toggle-mark', 'next-question'])

// 計算屬性

const getChapterDisplayTitle = computed(() => {
  if (!props.chapterTitle) return ''

  if (props.currentLanguage === '中文') {
    return `中文${props.chapterTitle}`
  } else if (props.currentLanguage === '英文') {
    return `英文${props.chapterTitle}`
  } else {
    return props.chapterTitle
  }
})

// 鍵盤事件處理
const handleKeyPress = (event) => {
  // 只在考試模式且當前題目是複選題時處理空白鍵
  if (props.mode === 'exam' && event.code === 'Space') {
    const currentQuestion = props.questions[props.currentIndex]
    if (currentQuestion && currentQuestion.type === 'multiple') {
      event.preventDefault()
      emit('next-question')
    }
  }
}

// 生命週期
onMounted(() => {
  document.addEventListener('keydown', handleKeyPress)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyPress)
})

// 方法

const getButtonClass = (index) => {
  const classes = []

  if (index === props.currentIndex) {
    classes.push('current')
  } else if (props.mode === 'exam') {
    // 檢查是否被標記
    if (props.markedQuestions.includes(index)) {
      classes.push('marked')
    } else {
      // 考試模式
      const userAnswer = props.userAnswers[index]
      if (userAnswer !== undefined && userAnswer !== -1 && userAnswer !== null) {
        if (Array.isArray(userAnswer) && userAnswer.length > 0) {
          classes.push('answered')
        } else if (!Array.isArray(userAnswer)) {
          classes.push('answered')
        } else {
          classes.push('unanswered')
        }
      } else {
        classes.push('unanswered')
      }
    }
  } else if (props.mode === 'review') {
    // 回顧模式
    const userAnswer = props.userAnswers[index]
    const correctAnswer = props.correctAnswers[index]

    if (isAnswerCorrect(userAnswer, correctAnswer)) {
      classes.push('correct')
    } else {
      classes.push('incorrect')
    }
  } else if (props.mode === 'questionBank') {
    // 題庫模式
    classes.push('question-bank')
  }

  return classes
}

const isAnswerCorrect = (userAnswer, correctAnswer) => {
  // 處理複選題
  if (Array.isArray(correctAnswer)) {
    if (!Array.isArray(userAnswer)) return false
    if (userAnswer.length !== correctAnswer.length) return false
    return userAnswer.every(ans => correctAnswer.includes(ans)) &&
      correctAnswer.every(ans => userAnswer.includes(ans))
  }
  // 處理單選題
  return userAnswer === correctAnswer
}

const jumpToQuestion = (index) => {
  emit('jump-to-question', index)
}
</script>

<style scoped>
.answer-card {
  position: sticky;
  top: 96px;
  /* 為 fixed navbar 留出空間 */
}

.answer-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 8px;
}

.answer-btn {
  width: 40px;
  height: 40px;
  border: 2px solid #e1e5e9;
  background: white;
  border-radius: 8px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.answer-btn:hover {
  transform: scale(1.1);
}

/* 考試模式樣式 */
.answer-btn.answered {
  background: #56cc9d;
  border-color: #56cc9d;
  color: white;
}

.answer-btn.unanswered {
  background: #f8f9fa;
  border-color: #dee2e6;
  color: #6c757d;
}

.answer-btn.marked {
  background: #ffc107;
  border-color: #ffc107;
  color: #212529;
  font-weight: bold;
}

.answer-btn.current {
  background: #667eea;
  border-color: #667eea;
  color: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

/* 回顧模式樣式 */
.answer-btn.correct {
  background: #56cc9d;
  border-color: #56cc9d;
  color: white;
}

.answer-btn.incorrect {
  background: #ffadad;
  border-color: #ffadad;
  color: #e22b3d;
}

/* 題庫模式樣式 */
.answer-btn.question-bank {
  background: #20c997;
  border-color: #20c997;
  color: white;
}

.legend-items {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.legend-color.answered {
  background: #56cc9d;
}

.legend-color.unanswered {
  background: #f8f9fa;
}

.legend-color.marked {
  background: #ffc107;
}

.legend-color.current {
  background: #667eea;
}

.legend-color.correct {
  background: #56cc9d;
}

.legend-color.incorrect {
  background: #ffadad;
}

.legend-color.question-bank {
  background: #20c997;
}

@media (max-width: 768px) {
  .answer-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .answer-btn {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }

  .legend-items {
    gap: 10px;
  }
}
</style>
