<template>
  <div class="col-6 mx-auto">
    <div class="exam-card">
      <div class="text-center mb-4">
        <h1 class="display-4 gradient-text fw-bold mb-3">📚 註冊帳號</h1>
        <p class="text-muted fs-5">建立您的考試練習帳號</p>
      </div>

      <form @submit.prevent="handleRegister">
        <div class="mb-3">
          <div class="d-flex gap-2">
            <label class="form-label fw-semibold">電子郵件</label>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" v-model="useGmailSuffix" id="gmailSuffix">
              <label class="form-check-label" for="gmailSuffix">
                Gmail
              </label>
            </div>
          </div>

          <div class="input-group">
            <input :type="useGmailSuffix ? 'text' : 'email'" class="form-control form-control-lg" v-model="emailInput"
              :placeholder="useGmailSuffix ? '請輸入用戶名' : '請輸入電子郵件'" required>
            <span v-if="useGmailSuffix" class="input-group-text">@gmail.com</span>
          </div>

        </div>

        <div class="mb-3">
          <label class="form-label fw-semibold">密碼</label>
          <input type="password" class="form-control form-control-lg" v-model="password" placeholder="請輸入密碼（至少6個字元）"
            required minlength="6">
        </div>

        <div class="mb-4">
          <label class="form-label fw-semibold">確認密碼</label>
          <input type="password" class="form-control form-control-lg" v-model="confirmPassword" placeholder="請再次輸入密碼"
            required>
        </div>

        <button type="submit"
          class="btn btn-gradient btn-lg w-100 d-flex align-items-center justify-content-center gap-2"
          :disabled="!finalEmail || !password || !confirmPassword || isLoading">
          <i class="fas fa-user-plus" v-if="!isLoading"></i>
          <div class="spinner-border spinner-border-sm" role="status" v-if="isLoading">
            <span class="visually-hidden">Loading...</span>
          </div>
          {{ isLoading ? '註冊中...' : '註冊帳號' }}
        </button>
      </form>

      <div v-if="errorMessage" class="alert alert-danger mt-3 d-flex align-items-center gap-2">
        <i class="fas fa-exclamation-triangle"></i>
        {{ errorMessage }}
      </div>

      <div v-if="successMessage" class="alert alert-success mt-3 d-flex align-items-center gap-2">
        <i class="fas fa-check-circle"></i>
        {{ successMessage }}
      </div>

      <div class="text-center mt-4">
        <p class="text-muted">
          已有帳號？
          <router-link to="/" class="text-decoration-none fw-semibold">
            立即登入
          </router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { registerUser } from '../firebase/auth'

const router = useRouter()

const emailInput = ref('')
const useGmailSuffix = ref(false)
const password = ref('')
const confirmPassword = ref('')
const errorMessage = ref('')
const successMessage = ref('')
const isLoading = ref(false)

// 計算最終的電子郵件地址
const finalEmail = computed(() => {
  if (!emailInput.value) return ''

  if (useGmailSuffix.value) {
    // 如果使用 Gmail 後綴，確保輸入的是用戶名部分
    const username = emailInput.value.replace(/@.*$/, '') // 移除任何現有的 @ 部分
    return `${username}@gmail.com`
  } else {
    return emailInput.value
  }
})



const handleRegister = async () => {
  errorMessage.value = ''
  successMessage.value = ''

  // 驗證密碼
  if (password.value !== confirmPassword.value) {
    errorMessage.value = '密碼與確認密碼不符'
    return
  }

  if (password.value.length < 6) {
    errorMessage.value = '密碼至少需要6個字元'
    return
  }

  isLoading.value = true

  try {
    const result = await registerUser(finalEmail.value, password.value)

    if (result.success) {
      successMessage.value = '註冊成功！正在跳轉到登入頁面...'
      setTimeout(() => {
        router.push('/')
      }, 2000)
    } else {
      // 處理 Firebase 錯誤訊息
      if (result.error.includes('email-already-in-use')) {
        errorMessage.value = '此電子郵件已被註冊'
      } else if (result.error.includes('weak-password')) {
        errorMessage.value = '密碼強度不足'
      } else if (result.error.includes('invalid-email')) {
        errorMessage.value = '電子郵件格式不正確'
      } else {
        errorMessage.value = '註冊失敗：' + result.error
      }
    }
  } catch (err) {
    errorMessage.value = '註冊過程中發生錯誤'
    console.error('註冊錯誤:', err)
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
@media (max-width: 480px) {
  .exam-card {
    padding: 30px 20px;
  }

  .display-4 {
    font-size: 2rem !important;
  }
}
</style>
